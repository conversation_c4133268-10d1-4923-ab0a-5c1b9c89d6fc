import { useEffect } from 'react'

const SEOHead = ({ 
  title, 
  description, 
  keywords, 
  canonical, 
  ogImage, 
  ogType = "website",
  structuredData,
  noindex = false 
}) => {
  useEffect(() => {
    // Update document title
    if (title) {
      document.title = title
    }

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription && description) {
      metaDescription.setAttribute('content', description)
    }

    // Update meta keywords
    const metaKeywords = document.querySelector('meta[name="keywords"]')
    if (metaKeywords && keywords) {
      metaKeywords.setAttribute('content', keywords)
    }

    // Update canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]')
    if (canonical) {
      if (!canonicalLink) {
        canonicalLink = document.createElement('link')
        canonicalLink.rel = 'canonical'
        document.head.appendChild(canonicalLink)
      }
      canonicalLink.href = canonical
    }

    // Update Open Graph tags
    const updateOGTag = (property, content) => {
      let ogTag = document.querySelector(`meta[property="${property}"]`)
      if (content) {
        if (!ogTag) {
          ogTag = document.createElement('meta')
          ogTag.setAttribute('property', property)
          document.head.appendChild(ogTag)
        }
        ogTag.setAttribute('content', content)
      }
    }

    updateOGTag('og:title', title)
    updateOGTag('og:description', description)
    updateOGTag('og:url', canonical)
    updateOGTag('og:image', ogImage)
    updateOGTag('og:type', ogType)

    // Update Twitter Card tags
    const updateTwitterTag = (name, content) => {
      let twitterTag = document.querySelector(`meta[name="${name}"]`)
      if (content) {
        if (!twitterTag) {
          twitterTag = document.createElement('meta')
          twitterTag.setAttribute('name', name)
          document.head.appendChild(twitterTag)
        }
        twitterTag.setAttribute('content', content)
      }
    }

    updateTwitterTag('twitter:title', title)
    updateTwitterTag('twitter:description', description)
    updateTwitterTag('twitter:image', ogImage)

    // Update robots meta tag
    const robotsTag = document.querySelector('meta[name="robots"]')
    if (robotsTag) {
      robotsTag.setAttribute('content', noindex ? 'noindex, nofollow' : 'index, follow')
    }

    // Add structured data
    if (structuredData) {
      const existingScript = document.querySelector('script[data-seo="structured-data"]')
      if (existingScript) {
        existingScript.remove()
      }

      const script = document.createElement('script')
      script.type = 'application/ld+json'
      script.setAttribute('data-seo', 'structured-data')
      script.textContent = JSON.stringify(structuredData)
      document.head.appendChild(script)
    }

    // Cleanup function
    return () => {
      // Remove page-specific structured data on unmount
      const pageScript = document.querySelector('script[data-seo="structured-data"]')
      if (pageScript) {
        pageScript.remove()
      }
    }
  }, [title, description, keywords, canonical, ogImage, ogType, structuredData, noindex])

  return null // This component doesn't render anything
}

export default SEOHead
