// Product Images
import fmt700Plus from './fmt-700-plus.jpg'
import fmt2100Ventilator from './fmt-2100-ventilator.jpg'
import transportVentilatorFmt700 from './transport-ventilator-fmt-700.jpg'
import portableVentilatorMachine from './portable-ventilator-machine.jpg'
import medicalVentilator from './medical-ventilator.jpg'
import anesthesiaWorkstationVentilator from './anesthesia-workstation-ventilator.jpg'
import anesthesiaWorkstation from './anesthesia-workstation.jpg'

// Export all product images
export const productImages = {
  'fmt-700-plus': fmt700Plus,
  'fmt-2100-ventilator': fmt2100Ventilator,
  'transport-ventilator-fmt-700': transportVentilatorFmt700,
  'portable-ventilator-machine': portableVentilatorMachine,
  'medical-ventilator': medicalVentilator,
  'anesthesia-workstation-ventilator': anesthesiaWorkstationVentilator,
  'anesthesia-workstation': anesthesiaWorkstation,
}

// Helper function to get product image by name
export const getProductImage = (imageName) => {
  return productImages[imageName] || '/api/placeholder/400/300'
}

export default productImages
